import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:carousel_slider/carousel_slider.dart';
import '../models/fishing_spot.dart';
import '../models/emoji_marker.dart';
import '../models/spot_photo.dart';
import '../models/spot_comment.dart';
import '../config/pocketbase_config.dart';
import '../services/service_locator.dart';
import '../services/unified_image_service.dart';
import '../services/spot_comment_service.dart';
import 'comment_dialog.dart';
import 'snackbar.dart';
import 'tiktok_comment_list.dart';

/// 钓点详情底部弹窗 - 重新设计版本
///
/// 特性：
/// - 现代化的UI设计
/// - 重点展示照片缩略图
/// - 分类展示钓点信息
/// - 良好的用户体验
class SpotDetailsSheet extends StatefulWidget {
  final FishingSpot spot;

  const SpotDetailsSheet({super.key, required this.spot});

  @override
  State<SpotDetailsSheet> createState() => _SpotDetailsSheetState();
}

class _SpotDetailsSheetState extends State<SpotDetailsSheet>
    with TickerProviderStateMixin {
  List<SpotPhoto> _photos = [];
  bool _isLoadingPhotos = true;
  int _currentPhotoIndex = 0;
  late TabController _tabController;
  final CarouselSliderController _carouselController =
      CarouselSliderController();

  // 页面状态
  bool _isLiked = false;
  bool _isUnliked = false; // 改名为更准确的倒赞状态
  bool _isFavorited = false;

  // 评论相关
  List<SpotComment> _comments = [];
  bool _isLoadingComments = true;
  final TextEditingController _commentInputController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();

  // 评论列表组件的引用
  final GlobalKey<TikTokCommentListState> _commentListKey = GlobalKey<TikTokCommentListState>();
  bool _isSubmittingComment = false;

  // 回复状态
  SpotComment? _replyingToComment;
  bool get _isReplying => _replyingToComment != null;

  // 统一图片服务
  final UnifiedImageService _imageService = UnifiedImageService();
  final SpotCommentService _commentService = SpotCommentService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _testCollectionAccess(); // 先测试集合访问权限
    _loadSpotPhotos();
    _loadUserInteractions();
    _loadComments();
  }

  /// 测试集合访问权限
  Future<void> _testCollectionAccess() async {
    try {
      debugPrint('🧪 [权限测试] 开始测试 spot_photos 集合访问权限');

      final pb = PocketBaseConfig.instance.client;

      // 测试1：尝试获取集合信息
      try {
        debugPrint('🧪 [权限测试] 测试1: 尝试查询空条件');
        final testRecords = await pb
            .collection('spot_photos')
            .getList(page: 1, perPage: 1);
        debugPrint(
          '✅ [权限测试] 测试1成功: 可以访问 spot_photos 集合，返回 ${testRecords.items.length} 条记录',
        );
      } catch (e) {
        debugPrint('❌ [权限测试] 测试1失败: $e');
      }

      // 测试2：尝试查询特定钓点的照片
      try {
        debugPrint('🧪 [权限测试] 测试2: 尝试查询特定钓点照片');
        final testRecords = await pb
            .collection('spot_photos')
            .getList(
              page: 1,
              perPage: 1,
              filter: 'spot_id = "${widget.spot.id}"',
            );
        debugPrint(
          '✅ [权限测试] 测试2成功: 可以查询特定钓点照片，返回 ${testRecords.items.length} 条记录',
        );
      } catch (e) {
        debugPrint('❌ [权限测试] 测试2失败: $e');
      }

      // 测试3：检查钓点本身的信息
      debugPrint('🧪 [权限测试] 钓点基本信息:');
      debugPrint('🧪 [权限测试] - 钓点ID: ${widget.spot.id}');
      debugPrint('🧪 [权限测试] - 钓点名称: ${widget.spot.name}');
      debugPrint('🧪 [权限测试] - 发布者ID: ${widget.spot.userId}');
      debugPrint('🧪 [权限测试] - 发布者名称: ${widget.spot.userName}');
      debugPrint('🧪 [权限测试] - 可见性: ${widget.spot.visibility}');
    } catch (e) {
      debugPrint('❌ [权限测试] 权限测试异常: $e');
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _commentInputController.dispose();
    _commentFocusNode.dispose();
    super.dispose();
  }

  /// 加载钓点照片
  Future<void> _loadSpotPhotos() async {
    try {
      debugPrint('🔍 [钓点详情] 开始加载钓点照片，钓点ID: ${widget.spot.id}');

      // 检查用户登录状态
      final currentUser = Services.auth.currentUser;
      debugPrint('🔍 [钓点详情] 用户登录状态: ${currentUser != null ? "已登录" : "未登录"}');
      debugPrint('🔍 [钓点详情] 钓点可见性: ${widget.spot.visibility}');
      if (currentUser != null) {
        debugPrint('🔍 [钓点详情] 当前用户ID: ${currentUser.id}');
      }

      // 从PocketBase直接获取钓点照片
      final pb = PocketBaseConfig.instance.client;
      debugPrint('🔍 [钓点详情] 准备查询 spot_photos 集合');
      debugPrint('🔍 [钓点详情] 查询条件: spot_id = "${widget.spot.id}"');

      final records = await pb
          .collection('spot_photos')
          .getFullList(
            filter: 'spot_id = "${widget.spot.id}"',
            sort: 'sort_order,created',
          );

      debugPrint('🔍 [钓点详情] PocketBase 查询完成');
      debugPrint('🔍 [钓点详情] 获取到 ${records.length} 张照片');

      final photos =
          records.map((record) => SpotPhoto.fromJson(record.toJson())).toList();

      // 打印照片URL信息
      for (int i = 0; i < photos.length; i++) {
        debugPrint('🔍 [钓点详情] 照片 $i:');
        debugPrint('  - 原图: ${photos[i].url}');
        debugPrint('  - 缩略图: ${photos[i].thumbnailUrl ?? "无"}');
        debugPrint('  - 类型: ${photos[i].type}');
      }

      // 检查组件是否仍然mounted
      if (mounted) {
        setState(() {
          _photos = photos;
          _isLoadingPhotos = false;
        });
        debugPrint('✅ [钓点详情] 照片加载完成，共 ${_photos.length} 张');
      }
    } catch (e) {
      debugPrint('❌ [钓点详情] 加载钓点照片失败: $e');
      debugPrint('❌ [钓点详情] 错误类型: ${e.runtimeType}');
      debugPrint('❌ [钓点详情] 错误详情: ${e.toString()}');

      // 检查是否是权限问题
      if (e.toString().contains('403') || e.toString().contains('Forbidden')) {
        debugPrint('❌ [钓点详情] 权限错误：可能是服务器权限规则未更新');
        debugPrint('💡 [钓点详情] 提示：需要更新服务器 spot_photos 集合的权限规则');
        debugPrint('💡 [钓点详情] 当前钓点可见性: ${widget.spot.visibility}');

        // 如果是公开钓点但仍然403，说明权限规则需要更新
        if (widget.spot.visibility == 'PUBLIC') {
          debugPrint('⚠️ [钓点详情] 公开钓点仍然403，权限规则需要更新！');
        }
      } else if (e.toString().contains('404') ||
          e.toString().contains('Not Found')) {
        debugPrint('❌ [钓点详情] 集合不存在：spot_photos 集合可能未创建');
      } else if (e.toString().contains('400') ||
          e.toString().contains('Bad Request')) {
        debugPrint('❌ [钓点详情] 请求错误：查询条件可能有问题');
      }

      // 尝试备用方案：通过钓点服务获取照片
      debugPrint('🔄 [钓点详情] 尝试备用方案：通过钓点服务获取照片');
      try {
        await _loadPhotosFromSpotService();
      } catch (fallbackError) {
        debugPrint('❌ [钓点详情] 备用方案也失败: $fallbackError');
      }

      // 检查组件是否仍然mounted
      if (mounted) {
        setState(() {
          _isLoadingPhotos = false;
        });
      }
    }
  }

  /// 加载用户互动状态
  Future<void> _loadUserInteractions() async {
    try {
      debugPrint('🔍 [互动状态] 开始加载用户互动状态');

      // 确保服务已加载用户互动记录
      await Services.spotInteraction.loadUserInteractions();

      if (mounted) {
        setState(() {
          _isLiked = Services.spotInteraction.isLiked(widget.spot.id);
          _isUnliked = Services.spotInteraction.isUnliked(widget.spot.id);
          _isFavorited = Services.userFavorite.isFavorited(widget.spot.id);
        });

        debugPrint('✅ [互动状态] 状态加载完成: 点赞=$_isLiked, 倒赞=$_isUnliked');
      }
    } catch (e) {
      debugPrint('❌ [互动状态] 加载用户互动状态失败: $e');
      if (mounted) {
        setState(() {
          _isLiked = false;
          _isUnliked = false;
          _isFavorited = false;
        });
      }
    }
  }

  /// 备用方案：通过钓点服务获取照片
  Future<void> _loadPhotosFromSpotService() async {
    try {
      debugPrint('🔄 [钓点详情] 开始备用照片加载方案');

      // 通过钓点服务重新获取钓点详情（包含照片信息）
      final spotWithPhotos = await Services.fishingSpot.getSpotById(
        widget.spot.id,
      );

      if (spotWithPhotos != null) {
        debugPrint('🔄 [钓点详情] 通过钓点服务获取到钓点信息');
        debugPrint('🔄 [钓点详情] 钓点照片URL列表: ${spotWithPhotos.photoUrls}');

        // 将URL转换为SpotPhoto对象（模拟）
        final photos = <SpotPhoto>[];
        for (int i = 0; i < spotWithPhotos.photoUrls.length; i++) {
          final photoUrl = spotWithPhotos.photoUrls[i];
          photos.add(
            SpotPhoto(
              id: 'temp_$i',
              spotId: widget.spot.id,
              userId: widget.spot.userId,
              filename: 'photo_$i.jpg',
              url: photoUrl,
              type: 'normal',
              sortOrder: i,
              created: DateTime.now(),
              updated: DateTime.now(),
            ),
          );
        }

        if (mounted) {
          setState(() {
            _photos = photos;
            _isLoadingPhotos = false;
          });
          debugPrint('✅ [钓点详情] 备用方案成功，加载了 ${photos.length} 张照片');
        }
      } else {
        debugPrint('❌ [钓点详情] 备用方案失败：无法获取钓点详情');
      }
    } catch (e) {
      debugPrint('❌ [钓点详情] 备用方案异常: $e');
      throw e;
    }
  }

  /// 获取签名URL（使用统一图片服务）
  Future<String> _getSignedUrl(String originalUrl) async {
    try {
      debugPrint('🔍 [钓点详情] 请求签名URL: $originalUrl');
      final signedUrl = await _imageService.getSignedUrl(originalUrl);
      debugPrint('✅ [钓点详情] 签名URL获取成功');
      return signedUrl;
    } catch (e) {
      debugPrint('❌ [钓点详情] 签名URL获取失败: $e');
      return originalUrl; // 返回原始URL作为后备
    }
  }

  /// 构建签名图片组件（使用缓存）
  Widget _buildSignedImage({
    required String originalUrl,
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
  }) {
    // 使用统一图片服务的缓存组件
    return _imageService.buildCachedSignedImage(
      originalUrl: originalUrl,
      fit: fit,
      width: width,
      height: height,
      placeholder: Container(
        width: width,
        height: height,
        color: Colors.grey.shade200,
        child: const Center(child: CircularProgressIndicator()),
      ),
      errorWidget: Container(
        width: width,
        height: height,
        color: Colors.grey.shade200,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.broken_image, size: 48, color: Colors.grey.shade400),
              const SizedBox(height: 8),
              Text('图片加载失败', style: TextStyle(color: Colors.grey.shade500)),
            ],
          ),
        ),
      ),
      isAvatar: false, // 钓点照片不是头像
    );
  }

  /// 构建照片查看器专用的签名图片组件（使用缓存）
  Widget _buildSignedImageForViewer(String originalUrl) {
    // 使用统一图片服务的缓存组件
    return _imageService.buildCachedSignedImage(
      originalUrl: originalUrl,
      fit: BoxFit.contain,
      placeholder: const Center(
        child: CircularProgressIndicator(color: Colors.white),
      ),
      errorWidget: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.broken_image, size: 64, color: Colors.white54),
            SizedBox(height: 16),
            Text(
              '图片加载失败',
              style: TextStyle(color: Colors.white54, fontSize: 16),
            ),
          ],
        ),
      ),
      isAvatar: false, // 钓点照片不是头像
    );
  }

  @override
  Widget build(BuildContext context) {
    // 获取输入法高度
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = keyboardHeight > 0;

    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: 12, bottom: 8),
            width: 48,
            height: 5,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2.5),
            ),
          ),

          // 英雄区域 - 照片轮播和基本信息（输入法弹出时缩小）
          _buildHeroSection(isKeyboardVisible: isKeyboardVisible),

          // 标签页内容
          Expanded(
            child: Column(
              children: [
                // 标签页导航
                _buildTabBar(),

                // 标签页内容
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // 详细信息页
                      _buildDetailsTab(),
                      // 照片页
                      _buildPhotosTab(),
                      // 评论页
                      _buildCommentsTab(),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // 底部操作栏
          _buildBottomActionBar(),
        ],
      ),
    );
  }

  /// 构建英雄区域 - 照片轮播和基本信息
  Widget _buildHeroSection({bool isKeyboardVisible = false}) {
    // 输入法弹出时缩小高度
    final heroHeight = isKeyboardVisible ? 120.0 : 280.0;

    return SizedBox(
      height: heroHeight,
      child: Stack(
        children: [
          // 照片轮播
          _buildPhotoCarousel(isCompact: isKeyboardVisible),

          // 渐变遮罩（紧凑模式时不显示）
          if (!isKeyboardVisible)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 120,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.7),
                    ],
                  ),
                ),
              ),
            ),

          // 钓点基本信息（紧凑模式时简化显示）
          Positioned(
            bottom: isKeyboardVisible ? 8 : 16,
            left: 16,
            right: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 钓点名称
                Text(
                  widget.spot.name,
                  style: TextStyle(
                    fontSize: isKeyboardVisible ? 16 : 24,
                    fontWeight: FontWeight.bold,
                    color: isKeyboardVisible ? Colors.black87 : Colors.white,
                    shadows: isKeyboardVisible ? null : [
                      const Shadow(
                        offset: Offset(0, 1),
                        blurRadius: 3,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 8),

                // 基本信息行
                Row(
                  children: [
                    // 发布者
                    const Icon(
                      Icons.person_outline,
                      size: 16,
                      color: Colors.white70,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      widget.spot.sharedBy,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),

                    const SizedBox(width: 16),

                    // 发布时间
                    const Icon(
                      Icons.access_time,
                      size: 16,
                      color: Colors.white70,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatDate(widget.spot.created),
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),

                    const Spacer(),

                    // 照片数量指示器
                    if (_photos.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.5),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${_currentPhotoIndex + 1}/${_photos.length}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建照片轮播
  Widget _buildPhotoCarousel({bool isCompact = false}) {
    if (_isLoadingPhotos) {
      return Container(
        color: Colors.grey.shade200,
        child: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_photos.isEmpty) {
      return Container(
        color: Colors.grey.shade100,
        child: Center(
          child: isCompact
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.photo_camera_outlined,
                    size: 24,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '暂无照片',
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
                  ),
                ],
              )
            : Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.photo_camera_outlined,
                    size: 48,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '暂无照片',
                    style: TextStyle(fontSize: 16, color: Colors.grey.shade500),
                  ),
                ],
              ),
        ),
      );
    }

    // 紧凑模式：显示为水平滚动的照片行
    if (isCompact) {
      return Container(
        height: 120,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: _photos.length,
          itemBuilder: (context, index) {
            final photo = _photos[index];
            final imageUrl = photo.url;

            return GestureDetector(
              onTap: () => _showPhotoViewer(index),
              child: Container(
                width: 120,
                margin: EdgeInsets.only(
                  left: index == 0 ? 16 : 4,
                  right: index == _photos.length - 1 ? 16 : 4,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: _buildSignedImage(
                    originalUrl: imageUrl,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            );
          },
        ),
      );
    }

    // 正常模式：轮播图
    return CarouselSlider.builder(
      carouselController: _carouselController,
      itemCount: _photos.length,
      itemBuilder: (context, index, realIndex) {
        final photo = _photos[index];
        // 使用缩略图URL，如果没有缩略图则使用原图
        final imageUrl = photo.url;

        debugPrint(
          '🖼️ [轮播] 照片 $index: 使用${photo.thumbnailUrl != null ? "缩略图" : "原图"} - $imageUrl',
        );

        return GestureDetector(
          onTap: () => _showPhotoViewer(index),
          child: SizedBox(
            width: double.infinity,
            child: _buildSignedImage(originalUrl: imageUrl, fit: BoxFit.cover),
          ),
        );
      },
      options: CarouselOptions(
        height: 280,
        viewportFraction: 1.0,
        enableInfiniteScroll: _photos.length > 1,
        autoPlay: false,
        onPageChanged: (index, reason) {
          setState(() {
            _currentPhotoIndex = index;
          });
        },
      ),
    );
  }

  /// 构建标签页导航栏
  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: Colors.blue.shade600,
        unselectedLabelColor: Colors.grey.shade600,
        indicatorColor: Colors.blue.shade600,
        indicatorWeight: 3,
        labelStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        unselectedLabelStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.normal,
        ),
        tabs: [
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.info_outline, size: 18),
                const SizedBox(width: 6),
                const Text('详情'),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.photo_library_outlined, size: 18),
                const SizedBox(width: 6),
                Text('照片${_photos.isNotEmpty ? '(${_photos.length})' : ''}'),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.chat_bubble_outline, size: 18),
                const SizedBox(width: 6),
                Text(
                  '评论${_comments.isNotEmpty ? '(${_comments.length})' : ''}',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建详情标签页
  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 钓点详细信息
          _buildDetailsSection(),

          const SizedBox(height: 20),

          // 描述信息
          _buildDescriptionSection(),

          const SizedBox(height: 20),

          // 位置信息
          _buildLocationSection(),
        ],
      ),
    );
  }

  /// 构建照片标签页
  Widget _buildPhotosTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_isLoadingPhotos)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(40),
                child: CircularProgressIndicator(),
              ),
            )
          else if (_photos.isEmpty)
            _buildNoPhotosPlaceholder()
          else
            _buildPhotoGrid(),
        ],
      ),
    );
  }

  /// 构建评论标签页
  Widget _buildCommentsTab() {
    return Column(
      children: [
        // 抖音风格评论列表 - 现在完全自管理状态
        Expanded(
          child: TikTokCommentList(
            key: _commentListKey, // 使用 GlobalKey 获取组件引用
            spotId: widget.spot.id,
            initialComments: _comments,
            initialIsLoading: _isLoadingComments,
            onNewComment: (comment) {
              // 通知父组件有新评论，但不触发重建
              debugPrint('🎯 [钓点详情] 收到新评论通知: ${comment.content}');
            },
            onReply: startReply, // 传递回复回调
          ),
        ),

        // 评论输入栏
        _buildCommentInputBar(),
      ],
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey.shade200, width: 1)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 点赞按钮
          _buildActionButton(
            icon:
                _isLiked
                    ? FontAwesomeIcons.solidThumbsUp
                    : FontAwesomeIcons.thumbsUp,
            label: '点赞',
            count: widget.spot.likes,
            color: _isLiked ? Colors.blue : Colors.grey.shade600,
            onTap: _handleLike,
          ),

          const SizedBox(width: 16),

          // 倒赞按钮
          _buildActionButton(
            icon:
                _isUnliked
                    ? FontAwesomeIcons.solidThumbsDown
                    : FontAwesomeIcons.thumbsDown,
            label: '倒赞',
            count: widget.spot.unlikes,
            color: _isUnliked ? Colors.red : Colors.grey.shade600,
            onTap: _handleDislike,
          ),

          const SizedBox(width: 16),

          // 收藏按钮
          _buildActionButton(
            icon:
                _isFavorited
                    ? FontAwesomeIcons.solidBookmark
                    : FontAwesomeIcons.bookmark,
            label: '收藏',
            count: null,
            color: _isFavorited ? Colors.orange : Colors.grey.shade600,
            onTap: _handleFavorite,
          ),

          const Spacer(),

          // 分享按钮
          ElevatedButton.icon(
            onPressed: _handleShare,
            icon: const FaIcon(FontAwesomeIcons.share, size: 16),
            label: const Text('分享'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required int? count,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FaIcon(icon, size: 20, color: color),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (count != null && count > 0) ...[
            const SizedBox(height: 2),
            Text(
              count.toString(),
              style: TextStyle(
                fontSize: 11,
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建位置信息区域
  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 位置信息标题
        const Row(
          children: [
            Icon(Icons.location_on, size: 20, color: Colors.red),
            SizedBox(width: 8),
            Text(
              '位置信息',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // 位置信息卡片
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.red.withValues(alpha: 0.2)),
          ),
          child: Column(
            children: [
              // 坐标信息
              _buildInfoItem(
                icon: Icons.gps_fixed,
                label: '坐标位置',
                value:
                    '${widget.spot.locationLatLng.latitude.toStringAsFixed(6)}, ${widget.spot.locationLatLng.longitude.toStringAsFixed(6)}',
                emoji: '📍',
              ),

              // 地址信息（如果有）
              if (widget.spot.address != null &&
                  widget.spot.address!.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildInfoItem(
                  icon: Icons.place,
                  label: '详细地址',
                  value: widget.spot.address!,
                  emoji: '🏠',
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// 构建标题区域
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 钓点名称
        Text(
          widget.spot.name,
          style: const TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),

        const SizedBox(height: 8),

        // 基本信息行
        Row(
          children: [
            // 发布者
            const Icon(Icons.person_outline, size: 16, color: Colors.grey),
            const SizedBox(width: 4),
            Text(
              widget.spot.sharedBy,
              style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
            ),

            const SizedBox(width: 16),

            // 发布时间
            const Icon(Icons.access_time, size: 16, color: Colors.grey),
            const SizedBox(width: 4),
            Text(
              _formatDate(widget.spot.created),
              style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建照片展示区域
  Widget _buildPhotoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 照片标题
        Row(
          children: [
            const Icon(Icons.photo_library, size: 20, color: Colors.blue),
            const SizedBox(width: 8),
            const Text(
              '钓点照片',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const Spacer(),
            if (_photos.isNotEmpty)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_photos.length}张',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue.shade700,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),

        const SizedBox(height: 12),

        // 照片网格或占位符
        _isLoadingPhotos
            ? _buildPhotoLoadingPlaceholder()
            : _photos.isEmpty
            ? _buildNoPhotosPlaceholder()
            : _buildPhotoGrid(),
      ],
    );
  }

  /// 构建照片加载占位符
  Widget _buildPhotoLoadingPlaceholder() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(child: CircularProgressIndicator()),
    );
  }

  /// 构建无照片占位符
  Widget _buildNoPhotosPlaceholder() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.photo_camera_outlined,
              size: 32,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 8),
            Text(
              '暂无照片',
              style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建照片网格
  Widget _buildPhotoGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: _photos.length > 6 ? 6 : _photos.length,
      itemBuilder: (context, index) {
        if (index == 5 && _photos.length > 6) {
          // 显示"更多"按钮
          return _buildMorePhotosButton(_photos.length - 5);
        }

        return _buildPhotoThumbnail(_photos[index], index);
      },
    );
  }

  /// 构建照片缩略图
  Widget _buildPhotoThumbnail(SpotPhoto photo, int index) {
    final imageUrl = photo.thumbnailUrl ?? photo.url;

    debugPrint(
      '🖼️ [网格] 照片 $index: 使用${photo.thumbnailUrl != null ? "缩略图" : "原图"} - $imageUrl',
    );

    return GestureDetector(
      onTap: () => _showPhotoViewer(index),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              // 照片
              _buildSignedImage(
                originalUrl: imageUrl,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
              ),

              // 全景照片标识
              if (photo.type == 'panorama')
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      '360°',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建更多照片按钮
  Widget _buildMorePhotosButton(int remainingCount) {
    return GestureDetector(
      onTap: () => _showAllPhotos(),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.more_horiz, color: Colors.white, size: 24),
              const SizedBox(height: 4),
              Text(
                '+$remainingCount',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return '今天';
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${date.month}月${date.day}日';
    }
  }

  /// 显示照片查看器
  void _showPhotoViewer(int initialIndex) {
    if (_photos.isEmpty) return;

    showDialog(
      context: context,
      builder:
          (context) => Dialog.fullscreen(
            backgroundColor: Colors.black,
            child: Stack(
              children: [
                // 照片轮播
                Center(
                  child: CarouselSlider.builder(
                    itemCount: _photos.length,
                    itemBuilder: (context, index, realIndex) {
                      final photo = _photos[index];
                      debugPrint('🖼️ [查看器] 照片 $index: 使用原图 - ${photo.url}');
                      return InteractiveViewer(
                        child: _buildSignedImageForViewer(photo.url),
                      );
                    },
                    options: CarouselOptions(
                      height: double.infinity,
                      viewportFraction: 1.0,
                      enableInfiniteScroll: _photos.length > 1,
                      initialPage: initialIndex,
                    ),
                  ),
                ),

                // 关闭按钮
                Positioned(
                  top: 50,
                  right: 20,
                  child: IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  /// 显示所有照片
  void _showAllPhotos() {
    // TODO: 实现显示所有照片的页面
    debugPrint('显示所有照片');
  }

  /// 构建钓点详细信息区域
  Widget _buildDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 详细信息标题
        const Row(
          children: [
            Icon(Icons.info_outline, size: 20, color: Colors.green),
            SizedBox(width: 8),
            Text(
              '钓点信息',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // 信息卡片
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.green.withValues(alpha: 0.2)),
          ),
          child: Column(
            children: [
              // 钓点类型和鱼类类型
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.location_on,
                      label: '钓点类型',
                      value: _getSpotTypeName(widget.spot.spotType),
                      emoji: widget.spot.spotEmoji,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.pets,
                      label: '鱼类类型',
                      value: _getFishTypeName(widget.spot.fishTypes),
                      emoji: widget.spot.fishEmoji,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // 坐标信息
              _buildInfoItem(
                icon: Icons.gps_fixed,
                label: '坐标位置',
                value:
                    '${widget.spot.locationLatLng.latitude.toStringAsFixed(6)}, ${widget.spot.locationLatLng.longitude.toStringAsFixed(6)}',
                emoji: '📍',
              ),

              // 地址信息（如果有）
              if (widget.spot.address != null &&
                  widget.spot.address!.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildInfoItem(
                  icon: Icons.place,
                  label: '详细地址',
                  value: widget.spot.address!,
                  emoji: '🏠',
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// 构建信息项
  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
    String? emoji,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // emoji或图标
        if (emoji != null)
          Text(emoji, style: const TextStyle(fontSize: 16))
        else
          Icon(icon, size: 16, color: Colors.green.shade600),

        const SizedBox(width: 8),

        // 标签和值
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建描述区域
  Widget _buildDescriptionSection() {
    if (widget.spot.description.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 描述标题
        const Row(
          children: [
            Icon(Icons.description_outlined, size: 20, color: Colors.orange),
            SizedBox(width: 8),
            Text(
              '详细描述',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // 描述内容
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.orange.withValues(alpha: 0.2)),
          ),
          child: Text(
            widget.spot.description,
            style: const TextStyle(
              fontSize: 15,
              color: Colors.black87,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建互动区域
  Widget _buildInteractionSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          // 点赞
          _buildInteractionButton(
            icon: FontAwesomeIcons.thumbsUp,
            label: '点赞',
            count: widget.spot.likes,
            color: Colors.blue,
            onTap: () => _handleLike(),
          ),

          // 不喜欢
          _buildInteractionButton(
            icon: FontAwesomeIcons.thumbsDown,
            label: '不喜欢',
            count: widget.spot.unlikes,
            color: Colors.red,
            onTap: () => _handleDislike(),
          ),

          // 评论
          _buildInteractionButton(
            icon: FontAwesomeIcons.comment,
            label: '评论',
            count: widget.spot.comments.length,
            color: Colors.green,
            onTap: () => _handleComment(),
          ),

          // 分享
          _buildInteractionButton(
            icon: FontAwesomeIcons.share,
            label: '分享',
            count: null,
            color: Colors.purple,
            onTap: () => _handleShare(),
          ),
        ],
      ),
    );
  }

  /// 构建互动按钮
  Widget _buildInteractionButton({
    required IconData icon,
    required String label,
    required int? count,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: FaIcon(icon, size: 18, color: color),
          ),
          const SizedBox(height: 6),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (count != null) ...[
            const SizedBox(height: 2),
            Text(
              count.toString(),
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建评论区域
  Widget _buildCommentsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 评论标题
        Row(
          children: [
            const Icon(
              Icons.chat_bubble_outline,
              size: 20,
              color: Colors.purple,
            ),
            const SizedBox(width: 8),
            const Text(
              '评论',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const Spacer(),
            if (_comments.isNotEmpty)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.purple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_comments.length}条',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.purple.shade700,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),

        const SizedBox(height: 12),

        // 抖音风格评论列表
        TikTokCommentList(
          key: ValueKey('comments_section_${widget.spot.id}'),
          spotId: widget.spot.id,
          initialComments: _comments,
          initialIsLoading: _isLoadingComments,
        ),
      ],
    );
  }

  /// 构建无评论占位符
  Widget _buildNoCommentsPlaceholder() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 32,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 8),
          Text(
            '暂无评论',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
          const SizedBox(height: 4),
          Text(
            '成为第一个评论的人吧',
            style: TextStyle(fontSize: 12, color: Colors.grey.shade400),
          ),
        ],
      ),
    );
  }



  /// 获取钓点类型名称
  String _getSpotTypeName(String? spotType) {
    if (spotType == null) return '未知';

    final marker = FishingSpotMarkers.spotTypes.firstWhere(
      (m) => m.type == spotType,
      orElse: () => FishingSpotMarkers.spotTypes.first,
    );
    return marker.name;
  }

  /// 获取鱼类类型名称
  String _getFishTypeName(String? fishType) {
    if (fishType == null) return '未知';

    final marker = FishingSpotMarkers.fishTypes.firstWhere(
      (m) => m.type == fishType,
      orElse: () => FishingSpotMarkers.fishTypes.first,
    );
    return marker.name;
  }

  /// 处理点赞
  Future<void> _handleLike() async {
    try {
      debugPrint('🔍 [点赞] 开始处理点赞操作');

      bool success = false;

      if (_isLiked) {
        // 如果已经点赞，则取消点赞
        success = await Services.spotInteraction.cancelLike(widget.spot.id);
        if (success && mounted) {
          setState(() {
            _isLiked = false;
          });
          debugPrint('✅ [点赞] 取消点赞成功');
        }
      } else {
        // 如果未点赞，则点赞
        success = await Services.spotInteraction.likeSpot(widget.spot.id);
        if (success && mounted) {
          setState(() {
            _isLiked = true;
            _isUnliked = false; // 点赞时自动取消倒赞
          });
          debugPrint('✅ [点赞] 点赞成功');
        }
      }

      if (!success) {
        debugPrint('❌ [点赞] 点赞操作失败');
        // TODO: 显示错误提示
      }
    } catch (e) {
      debugPrint('❌ [点赞] 点赞操作异常: $e');
      // TODO: 显示错误提示
    }
  }

  /// 处理倒赞
  Future<void> _handleDislike() async {
    try {
      debugPrint('🔍 [倒赞] 开始处理倒赞操作');

      bool success = false;

      if (_isUnliked) {
        // 如果已经倒赞，则取消倒赞
        success = await Services.spotInteraction.cancelUnlike(widget.spot.id);
        if (success && mounted) {
          setState(() {
            _isUnliked = false;
          });
          debugPrint('✅ [倒赞] 取消倒赞成功');
        }
      } else {
        // 如果未倒赞，则倒赞
        success = await Services.spotInteraction.unlikeSpot(widget.spot.id);
        if (success && mounted) {
          setState(() {
            _isUnliked = true;
            _isLiked = false; // 倒赞时自动取消点赞
          });
          debugPrint('✅ [倒赞] 倒赞成功');
        }
      }

      if (!success) {
        debugPrint('❌ [倒赞] 倒赞操作失败');
        // TODO: 显示错误提示
      }
    } catch (e) {
      debugPrint('❌ [倒赞] 倒赞操作异常: $e');
      // TODO: 显示错误提示
    }
  }

  /// 加载评论列表
  Future<void> _loadComments() async {
    try {
      debugPrint('💬 [钓点详情] 开始加载评论列表');

      final comments = await _commentService.getSpotComments(widget.spot.id);

      if (mounted) {
        setState(() {
          _comments = comments;
          _isLoadingComments = false;
        });
        debugPrint('✅ [钓点详情] 评论加载完成，共 ${_comments.length} 条');
        debugPrint('🔄 [钓点详情] setState调用完成，可能触发页面重建');
      }
    } catch (e) {
      debugPrint('❌ [钓点详情] 加载评论失败: $e');
      if (mounted) {
        setState(() {
          _isLoadingComments = false;
        });
      }
    }
  }

  /// 使用局部状态加载评论列表，避免触发整个页面重建
  Future<void> _loadCommentsWithLocalState(StateSetter setCommentState) async {
    try {
      debugPrint('💬 [钓点详情] 开始加载评论列表（局部状态）');

      final comments = await _commentService.getSpotComments(widget.spot.id);

      if (mounted) {
        // 更新局部状态，不触发整个页面重建
        setCommentState(() {
          _comments = comments;
          _isLoadingComments = false;
        });
        debugPrint('✅ [钓点详情] 评论加载完成（局部状态），共 ${_comments.length} 条');
        debugPrint('🎯 [钓点详情] 使用局部状态更新，避免图片重新加载');
      }
    } catch (e) {
      debugPrint('❌ [钓点详情] 加载评论失败（局部状态）: $e');
      if (mounted) {
        setCommentState(() {
          _isLoadingComments = false;
        });
      }
    }
  }

  /// 处理评论
  void _handleComment() {
    // 检查用户是否登录
    final currentUser = Services.auth.currentUser;
    if (currentUser == null) {
      SnackBarService.showWarning(context, '请先登录后再发表评论');
      return;
    }

    // 显示评论对话框
    showDialog(
      context: context,
      builder:
          (context) => CommentDialog(
            spotId: widget.spot.id,
            spotName: widget.spot.name,
            onSubmit: _submitComment,
          ),
    );
  }

  /// 提交评论
  Future<void> _submitComment(String content) async {
    try {
      debugPrint('💬 [钓点详情] 开始提交评论');

      // 使用评论列表组件的提交方法，避免在父组件中更新状态
      final commentListState = _commentListKey.currentState;
      if (commentListState != null) {
        final success = await commentListState.submitComment(
          content,
          replyToComment: _replyingToComment,
        );

        if (success) {
          final isReply = _replyingToComment != null;

          // 清除回复状态
          if (isReply) {
            setState(() {
              _replyingToComment = null;
            });
          }

          if (mounted) {
            SnackBarService.showSuccess(
              context,
              isReply ? '回复发布成功' : '评论发布成功',
            );
          }
          debugPrint('✅ [钓点详情] ${isReply ? "回复" : "评论"}提交成功，无需父组件状态更新');
        } else {
          final isReply = _replyingToComment != null;
          throw Exception('${isReply ? "回复" : "评论"}提交失败');
        }
      } else {
        throw Exception('评论组件未初始化');
      }
    } catch (e) {
      debugPrint('❌ [钓点详情] 评论提交失败: $e');

      if (mounted) {
        SnackBarService.showError(context, '评论发布失败，请稍后重试');
      }

      rethrow; // 重新抛出异常，让对话框处理
    }
  }

  /// 处理收藏
  Future<void> _handleFavorite() async {
    try {
      debugPrint('🔖 [收藏] 开始处理收藏操作');

      final success = await Services.userFavorite.toggleFavorite(
        widget.spot.id,
      );

      if (success && mounted) {
        setState(() {
          _isFavorited = Services.userFavorite.isFavorited(widget.spot.id);
        });

        // 显示成功提示
        _showSnackBar(
          _isFavorited ? '已添加到收藏' : '已取消收藏',
          _isFavorited ? Colors.orange : Colors.grey,
        );

        debugPrint('✅ [收藏] 收藏操作成功: ${_isFavorited ? "已收藏" : "已取消"}');
      } else {
        debugPrint('❌ [收藏] 收藏操作失败');

        // 显示错误提示
        if (mounted) {
          SnackBarService.showError(context, '操作失败，请稍后重试');
        }
      }
    } catch (e) {
      debugPrint('❌ [收藏] 收藏操作异常: $e');

      // 显示错误提示
      if (mounted) {
        SnackBarService.showError(context, '操作失败，请稍后重试');
      }
    }
  }

  /// 构建评论输入栏（微信风格）
  Widget _buildCommentInputBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey.shade200, width: 1)),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // 回复状态指示器
            if (_replyingToComment != null)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  border: Border(
                    bottom: BorderSide(color: Colors.blue.shade100, width: 1),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.reply,
                      size: 16,
                      color: Colors.blue.shade600,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '回复 @${_replyingToComment!.username}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: _cancelReply,
                      child: Icon(
                        Icons.close,
                        size: 18,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),

            // 输入框区域
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  // 输入框
                  Expanded(
                    child: Container(
                      constraints: const BoxConstraints(
                        minHeight: 36,
                        maxHeight: 100,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(18),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: TextField(
                        controller: _commentInputController,
                        focusNode: _commentFocusNode,
                        maxLines: null,
                        textInputAction: TextInputAction.send,
                        enabled: !_isSubmittingComment,
                        decoration: InputDecoration(
                          hintText: _replyingToComment != null
                            ? '回复 @${_replyingToComment!.username}...'
                            : '写评论...',
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                        ),
                        onChanged: (value) {
                          // 🔧 修复：移除不必要的setState调用，避免整个页面重建
                          // 发送按钮的状态通过ValueListenableBuilder来管理
                        },
                        onSubmitted: (value) {
                          if (value.trim().isNotEmpty && !_isSubmittingComment) {
                            _submitCommentFromInput();
                          }
                        },
                      ),
                    ),
                  ),

                  const SizedBox(width: 8),

                  // 发送按钮 - 使用ValueListenableBuilder避免整个页面重建
                  ValueListenableBuilder<TextEditingValue>(
                    valueListenable: _commentInputController,
                    builder: (context, value, child) {
                      final hasText = value.text.trim().isNotEmpty;
                      final canSend = hasText && !_isSubmittingComment;

                      return GestureDetector(
                        onTap: canSend ? _submitCommentFromInput : null,
                        child: Container(
                          width: 36,
                          height: 36,
                          decoration: BoxDecoration(
                            color: canSend ? Colors.blue : Colors.grey.shade300,
                            borderRadius: BorderRadius.circular(18),
                          ),
                          child:
                              _isSubmittingComment
                                  ? const Center(
                                    child: SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white,
                                        ),
                                      ),
                                    ),
                                  )
                                  : Icon(
                                    Icons.send,
                                    size: 18,
                                    color:
                                        hasText
                                            ? Colors.white
                                            : Colors.grey.shade500,
                                  ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 从输入栏提交评论
  Future<void> _submitCommentFromInput() async {
    final content = _commentInputController.text.trim();
    if (content.isEmpty) return;

    // 检查用户是否登录
    final currentUser = Services.auth.currentUser;
    if (currentUser == null) {
      _showSnackBar('请先登录后再发表评论', Colors.orange);
      return;
    }

    setState(() {
      _isSubmittingComment = true;
    });

    try {
      await _submitComment(content);

      // 清空输入框
      _commentInputController.clear();

      // 失去焦点
      _commentFocusNode.unfocus();
    } catch (e) {
      // 错误处理已在_submitComment中完成
    } finally {
      if (mounted) {
        setState(() {
          _isSubmittingComment = false;
        });
      }
    }
  }

  /// 显示SnackBar提示
  void _showSnackBar(String message, Color backgroundColor) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.only(
            bottom: 100, // 避免被底部弹窗遮挡
            left: 16,
            right: 16,
          ),
        ),
      );
    }
  }

  /// 处理分享
  void _handleShare() {
    // TODO: 实现分享功能
    debugPrint('分享钓点: ${widget.spot.id}');
  }

  /// 开始回复评论
  void startReply(SpotComment comment) {
    setState(() {
      _replyingToComment = comment;
    });

    // 聚焦到输入框
    _commentFocusNode.requestFocus();

    debugPrint('🔄 [回复] 开始回复评论: ${comment.username} - ${comment.content}');
  }

  /// 取消回复
  void _cancelReply() {
    setState(() {
      _replyingToComment = null;
    });

    // 清空输入框
    _commentInputController.clear();

    debugPrint('❌ [回复] 取消回复');
  }
}
